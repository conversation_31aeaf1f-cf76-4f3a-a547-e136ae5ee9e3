# VachanaTTS Usage Guide

## Overview
VachanaTTS is a powerful Text-to-Speech (TTS) system using VITS models that supports Thai language processing, voice cloning, video dubbing, and podcast generation.

## Installation Summary
✅ **Completed Setup:**
- Repository cloned successfully
- Virtual environment created (`venv/`)
- All dependencies installed
- Thai TTS model downloaded (`MMS-TTS-THAI-MALEV1`)
- OpenVoice models installed (v1 & v2 for voice cloning)
- Applications tested and verified working

## Quick Start

### Option 1: Using Launcher Scripts (Recommended)
```bash
# Navigate to project directory
cd /Users/<USER>/Desktop/content/tts-model

# Run English interface
./run_app.sh

# OR run Thai interface
./run_app_thai.sh
```

### Option 2: Manual Activation
```bash
# Navigate to project directory
cd /Users/<USER>/Desktop/content/tts-model

# IMPORTANT: Activate virtual environment first!
source venv/bin/activate

# Run English interface
python app.py

# OR run Thai interface
python app-th.py
```

Both will start a web interface at: `http://127.0.0.1:7860`

**⚠️ Important**: Always activate the virtual environment with `source venv/bin/activate` before running Python commands, or use the provided launcher scripts.

## Features Available

### 1. Basic Text-to-Speech
- Convert text to speech using VITS models
- Adjustable speaking rate (0.1x to 2.0x)
- Support for Thai language processing
- Multiple model support

### 2. Voice Cloning
- Clone voices using reference audio
- OpenVoice integration (models installed and ready)
- Multiple model versions (v1, v2)
- VAD (Voice Activity Detection) support

### 3. Video Dubbing
- Dub videos using SRT subtitle files
- Multiple speaker support
- Voice cloning for dubbing
- Automatic timing synchronization

### 4. Podcast Generation
- Generate multi-speaker podcasts
- Speaker identification (Speaker 1, Speaker 2, etc.)
- Natural conversation flow

## Using the Web Interface

### Basic TTS:
1. Enter text in the "Text to Speech" field
2. Select a model from the dropdown (currently: MMS-TTS-THAI-MALEV1)
3. Adjust speaking rate if needed
4. Click "Generate" to create audio

### Voice Cloning:
1. Check "Clone Voice" checkbox
2. Upload reference audio file
3. Select model version (v1 or v2)
4. Choose device (CPU/GPU)
5. Enable VAD if needed
6. Generate speech with cloned voice

### Download New Models:
1. Enter Hugging Face model name (e.g., "VIZINTZOR/MMS-TTS-THAI-MALEV1")
2. Click "Download" button
3. Model will be available in the dropdown

## Command Line Usage

### Download Additional Models:
```bash
# Activate environment
source venv/bin/activate

# Clone model from Hugging Face
git clone https://huggingface.co/MODEL_NAME models/MODEL_NAME
```

### Run Finetune WebUI:
```bash
# For model fine-tuning
python finetune/finetune-webui.py
```

## Model Requirements

### TTS Models:
- Place in `models/` directory
- Must be VITS-compatible models
- Hugging Face format preferred

### OpenVoice Models (for voice cloning):
- Download from: https://github.com/VYNCX/OpenVoice-WebUI/releases/download/Download/OPENVOICE_MODELS.zip
- Extract to `OPENVOICE_MODELS/` directory

## Example Workflows

### 1. Simple Thai TTS:
```
1. Run: python app-th.py
2. Open: http://127.0.0.1:7860
3. Enter Thai text: "สวัสดีครับ ยินดีที่ได้รู้จัก"
4. Select model: MMS-TTS-THAI-MALEV1
5. Click Generate
```

### 2. Voice Cloning:
```
1. Prepare reference audio (3-15 seconds)
2. Enable voice cloning
3. Upload reference audio
4. Enter target text
5. Generate cloned voice
```

### 3. Video Dubbing:
```
1. Prepare SRT subtitle file
2. Use format: "1, Speaker text" for multiple speakers
3. Upload video and SRT file
4. Select voice models for each speaker
5. Generate dubbed video
```

## Troubleshooting

### Common Issues:

**Model not found:**
- Ensure model is in `models/` directory
- Check model format compatibility

**Memory issues:**
- Use CPU instead of GPU for large models
- Reduce batch size in settings

**Audio quality:**
- Check input audio quality for voice cloning
- Adjust speaking rate for better results

### Performance Tips:

**For better performance:**
- Use GPU if available (CUDA)
- Keep reference audio 3-15 seconds
- Use high-quality reference audio for cloning

**For resource constraints:**
- Use CPU mode
- Reduce model complexity
- Process shorter text segments

## File Structure
```
tts-model/
├── venv/                    # Virtual environment
├── models/                  # TTS models directory
│   └── MMS-TTS-THAI-MALEV1/ # Downloaded Thai model
├── OPENVOICE_MODELS/        # Voice cloning models (v1 & v2 installed)
├── inference/               # Inference scripts
├── finetune/               # Model fine-tuning tools
├── openvoice/              # OpenVoice integration
├── app.py                  # Main English interface
├── app-th.py               # Thai interface
└── requirements.txt        # Dependencies
```

## Next Steps

1. **Download more models** from Hugging Face for different languages
2. **Test voice cloning** with the installed OpenVoice models
3. **Explore fine-tuning** with your own datasets
4. **Test different features** like dubbing and podcast generation

## Support

For issues or questions:
- Check the original repository: https://github.com/VYNCX/VachanaTTS
- Review model documentation on Hugging Face
- Ensure all dependencies are properly installed
