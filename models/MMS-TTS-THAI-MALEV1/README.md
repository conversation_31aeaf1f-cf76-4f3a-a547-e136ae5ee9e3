---
language:
- th
tags:
- text-to-speech
- Thai
---
โมเดลนี้ใช้ เสียงที่บันทึกจาก Play.ht : https://play.ht/ เพื่อนำมา finetune model.

Finetune โมเดลโค้ด GitHub : https://github.com/VYNCX/finetune-local-vits

[Finetune-colab](https://colab.research.google.com/drive/12qbpHnu7wYiTEoqh6_57_KUjp4gJkx2h?usp=sharing
) เทรนโมเดลเสียงด้วยตัวเองบน Google Colab

ใช้งาน บน local คอมพิวเตอร์ https://github.com/VYNCX/VachanaTTS

การใช้งาน : 
```py
import torch
from transformers import VitsTokenizer, VitsModel, set_seed
import scipy

tokenizer = VitsTokenizer.from_pretrained("VIZINTZOR/MMS-TTS-THAI-MALEV1",cache_dir="./mms")
model = VitsModel.from_pretrained("VIZINTZOR/MMS-TTS-THAI-MALEV1",cache_dir="./mms")

inputs = tokenizer(text="สวัสดีครับ นี่คือเสียงพูดภาษาไทย", return_tensors="pt")

set_seed(456)  # make deterministic

with torch.no_grad():
   outputs = model(**inputs)

waveform = outputs.waveform[0]

# Convert PyTorch tensor to NumPy array
waveform_array = waveform.numpy()

scipy.io.wavfile.write("techno_output.wav", rate=model.config.sampling_rate, data=waveform_array)
```
