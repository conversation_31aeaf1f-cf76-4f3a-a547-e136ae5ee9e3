#!/bin/bash

# VachanaTTS Application Launcher
# This script activates the virtual environment and runs the TTS application

echo "🚀 Starting VachanaTTS Application..."
echo "📁 Working directory: $(pwd)"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Error: Virtual environment not found!"
    echo "Please make sure you're in the correct directory: /Users/<USER>/Desktop/content/tts-model"
    exit 1
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Check if gradio is installed
if ! python -c "import gradio" 2>/dev/null; then
    echo "❌ Error: Gradio not found in virtual environment!"
    echo "Please run: source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

echo "✅ Virtual environment activated"
echo "🌐 Starting web interface..."
echo ""
echo "📱 The application will be available at: http://127.0.0.1:7860"
echo "🛑 Press Ctrl+C to stop the application"
echo ""

# Run the application
python app.py
